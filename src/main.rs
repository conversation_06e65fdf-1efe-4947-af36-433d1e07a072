use bevy::prelude::*;

mod player;
use player::PlayerPlugin;

fn spawn_floor(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    // Spawn the floor using the new required components approach
    commands.spawn((
        Mesh3d(meshes.add(Rectangle::new(10.0, 10.0))),
        MeshMaterial3d(materials.add(Color::srgb(0.3, 0.5, 0.3))),
        Transform::default(),
    ));
}

fn setup_camera_and_light(mut commands: Commands) {
    // Add a camera to see the scene
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(0.0, 10.0, 5.0).looking_at(Vec3::ZERO, Vec3::Z),
    ));

    // Add a light to illuminate the scene
    commands.spawn((
        DirectionalLight {
            illuminance: 10000.0,
            ..default()
        },
        Transform::from_rotation(Quat::from_euler(EulerRot::XYZ, -0.5, -0.5, 0.0)),
    ));
}

fn main() {
    App::new()
        .add_plugins((DefaultPlugins, PlayerPlugin))
        .add_systems(Startup, (setup_camera_and_light, spawn_floor))
        .run();
}
